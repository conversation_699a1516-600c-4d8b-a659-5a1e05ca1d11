<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste Stripe Integration - ProMandato</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://js.stripe.com/v3/"></script>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-3xl font-bold mb-8">Teste da Integração Stripe</h1>
        
        <!-- Status da Conexão -->
        <div class="bg-white rounded-lg shadow p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4">Status da Conexão</h2>
            <div id="connection-status" class="space-y-2">
                <div class="flex items-center">
                    <div id="stripe-status" class="w-3 h-3 rounded-full bg-gray-400 mr-2"></div>
                    <span>Stripe: <span id="stripe-text">Verificando...</span></span>
                </div>
                <div class="flex items-center">
                    <div id="backend-status" class="w-3 h-3 rounded-full bg-gray-400 mr-2"></div>
                    <span>Backend: <span id="backend-text">Verificando...</span></span>
                </div>
            </div>
        </div>

        <!-- Teste de Planos -->
        <div class="bg-white rounded-lg shadow p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4">Teste de Seleção de Planos</h2>
            <div class="grid md:grid-cols-3 gap-4">
                <!-- Plano Básico -->
                <div class="border rounded-lg p-4">
                    <h3 class="font-semibold">Básico</h3>
                    <p class="text-2xl font-bold text-blue-600">R$ 49,90/mês</p>
                    <button data-plan="basic" class="test-plan-btn w-full mt-4 bg-blue-600 text-white py-2 rounded hover:bg-blue-700">
                        Testar Básico
                    </button>
                </div>

                <!-- Plano Padrão -->
                <div class="border rounded-lg p-4 border-blue-500">
                    <h3 class="font-semibold">Padrão</h3>
                    <p class="text-2xl font-bold text-blue-600">R$ 99,90/mês</p>
                    <button data-plan="standard" class="test-plan-btn w-full mt-4 bg-blue-600 text-white py-2 rounded hover:bg-blue-700">
                        Testar Padrão
                    </button>
                </div>

                <!-- Plano Profissional -->
                <div class="border rounded-lg p-4">
                    <h3 class="font-semibold">Profissional</h3>
                    <p class="text-2xl font-bold text-blue-600">R$ 199,90/mês</p>
                    <button data-plan="professional" class="test-plan-btn w-full mt-4 bg-blue-600 text-white py-2 rounded hover:bg-blue-700">
                        Testar Profissional
                    </button>
                </div>
            </div>
        </div>

        <!-- Log de Atividades -->
        <div class="bg-white rounded-lg shadow p-6">
            <h2 class="text-xl font-semibold mb-4">Log de Atividades</h2>
            <div id="activity-log" class="bg-gray-50 rounded p-4 h-64 overflow-y-auto font-mono text-sm">
                <!-- Logs aparecerão aqui -->
            </div>
            <button onclick="clearLog()" class="mt-2 px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600">
                Limpar Log
            </button>
        </div>
    </div>

    <script>
        // Configuração
        const STRIPE_PUBLIC_KEY = 'pk_live_51QUu2mClUIoqY19kQholKzLBzhCKuYnrCqGAQPtJL3vfvp3BcuyhGopNqirFP9DzOcp1GMMPnoHCibwMIOGBroQq00Frc8LxxN';

        // Função para detectar URL da API
        function getApiUrl() {
            if (window.location.port === '5500' || window.location.hostname === '127.0.0.1') {
                return 'http://localhost:3002';
            }
            if (window.location.port === '3002') {
                return window.location.origin;
            }
            return 'http://localhost:3002';
        }

        const API_BASE_URL = getApiUrl();

        let stripe = null;

        // Função para adicionar log
        function addLog(message, type = 'info') {
            const logElement = document.getElementById('activity-log');
            const timestamp = new Date().toLocaleTimeString();
            const colorClass = type === 'error' ? 'text-red-600' : type === 'success' ? 'text-green-600' : 'text-blue-600';
            
            logElement.innerHTML += `<div class="${colorClass}">[${timestamp}] ${message}</div>`;
            logElement.scrollTop = logElement.scrollHeight;
        }

        // Função para limpar log
        function clearLog() {
            document.getElementById('activity-log').innerHTML = '';
        }

        // Função para atualizar status
        function updateStatus(element, status, text) {
            const statusElement = document.getElementById(element + '-status');
            const textElement = document.getElementById(element + '-text');
            
            statusElement.className = `w-3 h-3 rounded-full mr-2 ${
                status === 'success' ? 'bg-green-500' : 
                status === 'error' ? 'bg-red-500' : 'bg-yellow-500'
            }`;
            textElement.textContent = text;
        }

        // Inicializar Stripe
        async function initializeStripe() {
            try {
                addLog('Inicializando Stripe...');
                stripe = Stripe(STRIPE_PUBLIC_KEY);
                updateStatus('stripe', 'success', 'Conectado');
                addLog('Stripe inicializado com sucesso', 'success');
                return true;
            } catch (error) {
                updateStatus('stripe', 'error', 'Erro');
                addLog(`Erro ao inicializar Stripe: ${error.message}`, 'error');
                return false;
            }
        }

        // Testar conexão com backend
        async function testBackendConnection() {
            try {
                addLog('Testando conexão com backend...');
                const response = await fetch(`${API_BASE_URL}/api/health`);
                
                if (response.ok) {
                    const data = await response.json();
                    updateStatus('backend', 'success', 'Conectado');
                    addLog(`Backend conectado: ${data.message}`, 'success');
                    return true;
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                updateStatus('backend', 'error', 'Erro');
                addLog(`Erro ao conectar com backend: ${error.message}`, 'error');
                return false;
            }
        }

        // Testar seleção de plano
        async function testPlanSelection(planType) {
            try {
                addLog(`Testando seleção do plano: ${planType}`);
                
                // Dados de teste
                const testData = {
                    planType: planType,
                    billingCycle: 'monthly',
                    leadData: {
                        name: 'Teste Usuario',
                        email: '<EMAIL>',
                        phone: '(11) 99999-9999',
                        organization: 'Teste Org',
                        source: 'test_page'
                    },
                    successUrl: `${window.location.origin}/landingpage/success.html`,
                    cancelUrl: `${window.location.origin}/landingpage/index.html`
                };

                addLog('Enviando requisição para criar checkout session...');
                
                const response = await fetch(`${API_BASE_URL}/api/stripe/create-checkout-session`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(testData)
                });

                if (!response.ok) {
                    const errorData = await response.json();
                    throw new Error(errorData.message || `HTTP ${response.status}`);
                }

                const session = await response.json();
                addLog(`Checkout session criada: ${session.sessionId}`, 'success');
                
                // Simular redirecionamento (não fazer o redirect real em teste)
                addLog('Redirecionamento para Stripe seria feito aqui (simulado)', 'success');
                addLog(`URL do Stripe: https://checkout.stripe.com/pay/${session.sessionId}`, 'info');
                
                return true;

            } catch (error) {
                addLog(`Erro ao testar plano ${planType}: ${error.message}`, 'error');
                return false;
            }
        }

        // Event listeners
        document.addEventListener('DOMContentLoaded', async function() {
            addLog('Iniciando testes...');
            
            // Testar conexões
            await initializeStripe();
            await testBackendConnection();
            
            // Configurar botões de teste
            document.querySelectorAll('.test-plan-btn').forEach(button => {
                button.addEventListener('click', async (e) => {
                    const planType = e.target.getAttribute('data-plan');
                    e.target.disabled = true;
                    e.target.textContent = 'Testando...';
                    
                    await testPlanSelection(planType);
                    
                    e.target.disabled = false;
                    e.target.textContent = `Testar ${planType.charAt(0).toUpperCase() + planType.slice(1)}`;
                });
            });
            
            addLog('Testes inicializados. Clique nos botões para testar os planos.', 'success');
        });
    </script>
</body>
</html>
