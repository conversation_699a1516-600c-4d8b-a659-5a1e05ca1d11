<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste CORS - ProMandato</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-3xl font-bold mb-8">Teste de CORS e Conectividade</h1>
        
        <!-- Status da Conexão -->
        <div class="bg-white rounded-lg shadow p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4">Status da Conexão</h2>
            <div id="connection-status" class="space-y-2">
                <div class="flex items-center">
                    <div id="health-status" class="w-3 h-3 rounded-full bg-gray-400 mr-2"></div>
                    <span>Health Check: <span id="health-text">Verificando...</span></span>
                </div>
                <div class="flex items-center">
                    <div id="analytics-status" class="w-3 h-3 rounded-full bg-gray-400 mr-2"></div>
                    <span>Analytics: <span id="analytics-text">Verificando...</span></span>
                </div>
                <div class="flex items-center">
                    <div id="stripe-status" class="w-3 h-3 rounded-full bg-gray-400 mr-2"></div>
                    <span>Stripe: <span id="stripe-text">Verificando...</span></span>
                </div>
            </div>
        </div>

        <!-- Informações do Ambiente -->
        <div class="bg-white rounded-lg shadow p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4">Informações do Ambiente</h2>
            <div id="environment-info" class="space-y-1 text-sm font-mono">
                <!-- Será preenchido via JavaScript -->
            </div>
        </div>

        <!-- Teste de Endpoints -->
        <div class="bg-white rounded-lg shadow p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4">Teste de Endpoints</h2>
            <div class="space-y-4">
                <button onclick="testHealthCheck()" class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">
                    Testar Health Check
                </button>
                <button onclick="testAnalytics()" class="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700">
                    Testar Analytics
                </button>
                <button onclick="testStripe()" class="bg-purple-600 text-white px-4 py-2 rounded hover:bg-purple-700">
                    Testar Stripe
                </button>
            </div>
        </div>

        <!-- Log de Atividades -->
        <div class="bg-white rounded-lg shadow p-6">
            <h2 class="text-xl font-semibold mb-4">Log de Atividades</h2>
            <div id="activity-log" class="bg-gray-50 rounded p-4 h-64 overflow-y-auto font-mono text-sm">
                <!-- Logs aparecerão aqui -->
            </div>
            <button onclick="clearLog()" class="mt-2 px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600">
                Limpar Log
            </button>
        </div>
    </div>

    <script>
        // Função para detectar URL da API
        function getApiUrl() {
            if (window.location.hostname === 'localhost' || 
                window.location.hostname === '127.0.0.1' || 
                window.location.port === '5500' ||
                window.location.port === '8080' ||
                window.location.port === '8000') {
                return 'http://localhost:3002';
            }
            
            if (window.location.port === '3002') {
                return window.location.origin;
            }
            
            return 'http://localhost:3002';
        }

        const API_BASE_URL = getApiUrl();

        // Função para adicionar log
        function addLog(message, type = 'info') {
            const logElement = document.getElementById('activity-log');
            const timestamp = new Date().toLocaleTimeString();
            const colorClass = type === 'error' ? 'text-red-600' : type === 'success' ? 'text-green-600' : 'text-blue-600';
            
            logElement.innerHTML += `<div class="${colorClass}">[${timestamp}] ${message}</div>`;
            logElement.scrollTop = logElement.scrollHeight;
        }

        // Função para limpar log
        function clearLog() {
            document.getElementById('activity-log').innerHTML = '';
        }

        // Função para atualizar status
        function updateStatus(element, status, text) {
            const statusElement = document.getElementById(element + '-status');
            const textElement = document.getElementById(element + '-text');
            
            statusElement.className = `w-3 h-3 rounded-full mr-2 ${
                status === 'success' ? 'bg-green-500' : 
                status === 'error' ? 'bg-red-500' : 'bg-yellow-500'
            }`;
            textElement.textContent = text;
        }

        // Mostrar informações do ambiente
        function showEnvironmentInfo() {
            const info = {
                'URL Atual': window.location.href,
                'Hostname': window.location.hostname,
                'Porta': window.location.port || '80/443',
                'Protocolo': window.location.protocol,
                'Origin': window.location.origin,
                'API URL Detectada': API_BASE_URL,
                'User Agent': navigator.userAgent.substring(0, 100) + '...'
            };

            const infoElement = document.getElementById('environment-info');
            infoElement.innerHTML = '';
            
            Object.entries(info).forEach(([key, value]) => {
                infoElement.innerHTML += `<div><strong>${key}:</strong> ${value}</div>`;
            });
        }

        // Testar health check
        async function testHealthCheck() {
            try {
                addLog('Testando health check...');
                const response = await fetch(`${API_BASE_URL}/api/health`);
                
                if (response.ok) {
                    const data = await response.json();
                    updateStatus('health', 'success', 'OK');
                    addLog(`Health check OK: ${data.message}`, 'success');
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                updateStatus('health', 'error', 'Erro');
                addLog(`Health check falhou: ${error.message}`, 'error');
            }
        }

        // Testar analytics
        async function testAnalytics() {
            try {
                addLog('Testando analytics...');
                const response = await fetch(`${API_BASE_URL}/api/analytics/landing-page`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        event: 'test_event',
                        sessionId: 'test_session_' + Date.now(),
                        timestamp: new Date().toISOString()
                    })
                });
                
                if (response.ok) {
                    const data = await response.json();
                    updateStatus('analytics', 'success', 'OK');
                    addLog(`Analytics OK: ${data.message}`, 'success');
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                updateStatus('analytics', 'error', 'Erro');
                addLog(`Analytics falhou: ${error.message}`, 'error');
            }
        }

        // Testar Stripe
        async function testStripe() {
            try {
                addLog('Testando Stripe...');
                const response = await fetch(`${API_BASE_URL}/api/stripe/create-checkout-session`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        planType: 'basic',
                        billingCycle: 'monthly',
                        leadData: {
                            name: 'Teste CORS',
                            email: '<EMAIL>',
                            source: 'cors_test'
                        }
                    })
                });
                
                if (response.ok) {
                    const data = await response.json();
                    updateStatus('stripe', 'success', 'OK');
                    addLog(`Stripe OK: Session ${data.sessionId}`, 'success');
                } else {
                    const errorText = await response.text();
                    throw new Error(`HTTP ${response.status}: ${errorText}`);
                }
            } catch (error) {
                updateStatus('stripe', 'error', 'Erro');
                addLog(`Stripe falhou: ${error.message}`, 'error');
            }
        }

        // Inicializar quando a página carregar
        document.addEventListener('DOMContentLoaded', function() {
            addLog('Iniciando testes de CORS...');
            showEnvironmentInfo();
            
            // Executar testes automaticamente
            setTimeout(testHealthCheck, 500);
            setTimeout(testAnalytics, 1000);
            setTimeout(testStripe, 1500);
        });
    </script>
</body>
</html>
